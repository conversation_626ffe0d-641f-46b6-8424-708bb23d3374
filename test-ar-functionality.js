/**
 * AR Functionality Test Script
 * 
 * This script provides automated tests for AR functionality.
 * Run with: node test-ar-functionality.js
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  requiredFiles: [
    'App.tsx',
    'ARTestingApp.tsx',
    'apple-scene-ar-glb.tsx',
    'dog-scene-ar.tsx',
    'duck-scene-ar.tsx',
  ],
  requiredAssets: [
    'res/apple-trigger-1.png',
    'res/dog/dog-trigger-white.png',
    'res/trigger-duck.png',
    'res/apple_5_time_smaller.glb',
    'res/dog-v2/dog_obj.obj',
    'res/duck_obj.obj',
  ],
  requiredDependencies: [
    '@reactvision/react-viro',
    'react-native-tts',
  ]
};

// Test results
let testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    'info': '📋',
    'pass': '✅',
    'fail': '❌',
    'warn': '⚠️'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
  
  testResults.details.push({
    timestamp,
    type,
    message
  });
}

function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

function readFileContent(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return null;
  }
}

// Test functions
function testRequiredFiles() {
  log('Testing required files...', 'info');
  
  TEST_CONFIG.requiredFiles.forEach(file => {
    if (fileExists(file)) {
      log(`Found: ${file}`, 'pass');
      testResults.passed++;
    } else {
      log(`Missing: ${file}`, 'fail');
      testResults.failed++;
    }
  });
}

function testRequiredAssets() {
  log('Testing required assets...', 'info');
  
  TEST_CONFIG.requiredAssets.forEach(asset => {
    if (fileExists(asset)) {
      log(`Found: ${asset}`, 'pass');
      testResults.passed++;
    } else {
      log(`Missing: ${asset}`, 'fail');
      testResults.failed++;
    }
  });
}

function testPackageJson() {
  log('Testing package.json dependencies...', 'info');
  
  const packageJsonPath = 'package.json';
  if (!fileExists(packageJsonPath)) {
    log('package.json not found', 'fail');
    testResults.failed++;
    return;
  }
  
  const packageContent = readFileContent(packageJsonPath);
  if (!packageContent) {
    log('Could not read package.json', 'fail');
    testResults.failed++;
    return;
  }
  
  try {
    const packageJson = JSON.parse(packageContent);
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };
    
    TEST_CONFIG.requiredDependencies.forEach(dep => {
      if (allDeps[dep]) {
        log(`Dependency found: ${dep} (${allDeps[dep]})`, 'pass');
        testResults.passed++;
      } else {
        log(`Missing dependency: ${dep}`, 'fail');
        testResults.failed++;
      }
    });
  } catch (error) {
    log(`Error parsing package.json: ${error.message}`, 'fail');
    testResults.failed++;
  }
}

function testARSceneImplementations() {
  log('Testing AR scene implementations...', 'info');
  
  const sceneFiles = [
    'apple-scene-ar-glb.tsx',
    'dog-scene-ar.tsx',
    'duck-scene-ar.tsx'
  ];
  
  sceneFiles.forEach(file => {
    const content = readFileContent(file);
    if (!content) {
      log(`Could not read ${file}`, 'fail');
      testResults.failed++;
      return;
    }
    
    // Check for required imports
    const requiredImports = [
      'ViroARScene',
      'ViroARImageMarker',
      'ViroARTrackingTargets'
    ];
    
    requiredImports.forEach(importName => {
      if (content.includes(importName)) {
        log(`${file}: Found import ${importName}`, 'pass');
        testResults.passed++;
      } else {
        log(`${file}: Missing import ${importName}`, 'fail');
        testResults.failed++;
      }
    });
    
    // Check for tracking target creation
    if (content.includes('ViroARTrackingTargets.createTargets')) {
      log(`${file}: Has tracking target configuration`, 'pass');
      testResults.passed++;
    } else {
      log(`${file}: Missing tracking target configuration`, 'fail');
      testResults.failed++;
    }
    
    // Check for onAnchorFound callback
    if (content.includes('onAnchorFound')) {
      log(`${file}: Has anchor found callback`, 'pass');
      testResults.passed++;
    } else {
      log(`${file}: Missing anchor found callback`, 'warn');
      testResults.warnings++;
    }
  });
}

function testAppConfiguration() {
  log('Testing App.tsx configuration...', 'info');
  
  const appContent = readFileContent('App.tsx');
  if (!appContent) {
    log('Could not read App.tsx', 'fail');
    testResults.failed++;
    return;
  }
  
  // Check for camera permission handling
  if (appContent.includes('PermissionsAndroid.PERMISSIONS.CAMERA')) {
    log('App.tsx: Has camera permission handling', 'pass');
    testResults.passed++;
  } else {
    log('App.tsx: Missing camera permission handling', 'fail');
    testResults.failed++;
  }
  
  // Check for TTS integration
  if (appContent.includes('react-native-tts')) {
    log('App.tsx: Has TTS integration', 'pass');
    testResults.passed++;
  } else {
    log('App.tsx: Missing TTS integration', 'warn');
    testResults.warnings++;
  }
  
  // Check for AR scene navigator
  if (appContent.includes('ViroARSceneNavigator')) {
    log('App.tsx: Has AR scene navigator', 'pass');
    testResults.passed++;
  } else {
    log('App.tsx: Missing AR scene navigator', 'fail');
    testResults.failed++;
  }
}

function generateTestReport() {
  log('Generating test report...', 'info');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: testResults.passed + testResults.failed + testResults.warnings,
      passed: testResults.passed,
      failed: testResults.failed,
      warnings: testResults.warnings,
      success_rate: Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)
    },
    details: testResults.details
  };
  
  // Write report to file
  fs.writeFileSync('ar-test-report.json', JSON.stringify(report, null, 2));
  
  // Print summary
  console.log('\n' + '='.repeat(50));
  console.log('AR FUNCTIONALITY TEST REPORT');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${report.summary.total}`);
  console.log(`✅ Passed: ${report.summary.passed}`);
  console.log(`❌ Failed: ${report.summary.failed}`);
  console.log(`⚠️  Warnings: ${report.summary.warnings}`);
  console.log(`Success Rate: ${report.summary.success_rate}%`);
  console.log('='.repeat(50));
  
  if (report.summary.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.details
      .filter(detail => detail.type === 'fail')
      .forEach(detail => console.log(`  - ${detail.message}`));
  }
  
  if (report.summary.warnings > 0) {
    console.log('\n⚠️  WARNINGS:');
    testResults.details
      .filter(detail => detail.type === 'warn')
      .forEach(detail => console.log(`  - ${detail.message}`));
  }
  
  console.log(`\n📄 Detailed report saved to: ar-test-report.json`);
}

// Main test execution
function runTests() {
  console.log('🚀 Starting AR Functionality Tests...\n');
  
  testRequiredFiles();
  testRequiredAssets();
  testPackageJson();
  testARSceneImplementations();
  testAppConfiguration();
  
  generateTestReport();
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  TEST_CONFIG,
  testResults
};
